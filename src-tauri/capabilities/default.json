{"$schema": "https://schema.tauri.app/capabilities/v1", "identifier": "default", "description": "Default capabilities for JustCooked", "windows": ["main"], "permissions": ["core:default", "core:window:default", "core:webview:default", "fs:default", "fs:allow-write-text-file", "fs:allow-read-text-file", "fs:allow-create", "fs:allow-mkdir", "fs:allow-remove", "dialog:default", "http:default", "http:allow-fetch", "shell:default", {"identifier": "fs:scope", "allow": [{"path": "$DATA/**", "read": true, "write": true}]}]}