#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::{Duration, Instant};

    /// Performance benchmark for individual ingredient parsing
    #[tokio::test]
    async fn test_individual_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour",
            "1 tablespoon olive oil",
            "3 large eggs",
            "1/2 cup sugar",
            "1 teaspoon vanilla extract",
        ];

        let start_time = Instant::now();
        let mut successful_parses = 0;

        for ingredient in &test_ingredients {
            if let Ok(Some(_)) = parser.parse_ingredient(ingredient, None).await {
                successful_parses += 1;
            }
        }

        let elapsed = start_time.elapsed();
        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Individual parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", successful_parses, test_ingredients.len());

        // Performance targets from KALOSM-INTEGRATION.md
        // Individual Parsing: < 500ms per ingredient
        assert!(avg_time_per_ingredient < 500.0, 
            "Average parsing time {:.2}ms exceeds target of 500ms", avg_time_per_ingredient);
    }

    /// Performance benchmark for batch ingredient parsing
    #[tokio::test]
    async fn test_batch_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour".to_string(),
            "1 tablespoon olive oil".to_string(),
            "3 large eggs".to_string(),
            "1/2 cup sugar".to_string(),
            "1 teaspoon vanilla extract".to_string(),
            "1 cup milk".to_string(),
            "2 teaspoons baking powder".to_string(),
            "1/2 teaspoon salt".to_string(),
            "1/4 cup butter, melted".to_string(),
            "1 teaspoon cinnamon".to_string(),
        ];

        let start_time = Instant::now();
        let results = parser.parse_ingredients_batch(&test_ingredients).await;
        let elapsed = start_time.elapsed();

        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Batch parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", results.unwrap_or_default().len(), test_ingredients.len());

        // Performance targets from KALOSM-INTEGRATION.md
        // Batch Parsing: < 50ms per ingredient in batches
        assert!(avg_time_per_ingredient < 50.0, 
            "Average batch parsing time {:.2}ms exceeds target of 50ms", avg_time_per_ingredient);
    }

    /// Performance benchmark for chunked batch parsing
    #[tokio::test]
    async fn test_chunked_parsing_performance() {
        let parser = IngredientParser::new();
        
        // Create a larger dataset for chunked parsing
        let mut test_ingredients = Vec::new();
        let base_ingredients = vec![
            "2 cups all-purpose flour",
            "1 tablespoon olive oil", 
            "3 large eggs",
            "1/2 cup sugar",
            "1 teaspoon vanilla extract",
        ];

        // Repeat to create 25 ingredients (5 chunks of 5)
        for i in 0..5 {
            for ingredient in &base_ingredients {
                test_ingredients.push(format!("{} (batch {})", ingredient, i + 1));
            }
        }

        let start_time = Instant::now();
        let results = parser.parse_ingredients_chunked(&test_ingredients, 5).await;
        let elapsed = start_time.elapsed();

        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Chunked parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", results.unwrap_or_default().len(), test_ingredients.len());

        // Should be similar to batch parsing performance
        assert!(avg_time_per_ingredient < 100.0, 
            "Average chunked parsing time {:.2}ms exceeds reasonable threshold", avg_time_per_ingredient);
    }

    /// Test concurrent parsing performance with semaphore limiting
    #[tokio::test]
    async fn test_concurrent_parsing_performance() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups flour", "1 tbsp oil", "3 eggs", "1/2 cup sugar", "1 tsp vanilla",
            "1 cup milk", "2 tsp baking powder", "1/2 tsp salt", "1/4 cup butter", "1 tsp cinnamon",
        ];

        let start_time = Instant::now();
        
        // Create concurrent tasks
        let mut tasks = Vec::new();
        for ingredient in &test_ingredients {
            let task = parser.parse_ingredient(ingredient, None);
            tasks.push(task);
        }

        // Execute all tasks concurrently
        let results = futures::future::join_all(tasks).await;
        let elapsed = start_time.elapsed();

        let successful_parses = results.iter().filter(|r| r.is_ok()).count();
        let avg_time_per_ingredient = elapsed.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Concurrent parsing performance:");
        println!("  Total time: {}ms", elapsed.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);
        println!("  Successful parses: {}/{}", successful_parses, test_ingredients.len());

        // Concurrent parsing should be faster than sequential
        assert!(elapsed.as_millis() < 1000, 
            "Concurrent parsing took {}ms, should be under 1000ms", elapsed.as_millis());
    }

    /// Test memory usage patterns during parsing
    #[tokio::test]
    async fn test_memory_usage_patterns() {
        let parser = IngredientParser::new();
        
        // Get initial metrics
        let initial_metrics = parser.get_metrics().await;
        
        // Parse a batch of ingredients
        let test_ingredients = vec![
            "2 cups flour".to_string(),
            "1 tbsp oil".to_string(), 
            "3 eggs".to_string(),
            "1/2 cup sugar".to_string(),
            "1 tsp vanilla".to_string(),
        ];

        let _results = parser.parse_ingredients_batch(&test_ingredients).await;
        
        // Get final metrics
        let final_metrics = parser.get_metrics().await;
        
        println!("Memory usage metrics:");
        println!("  Initial requests: {}", initial_metrics.total_requests);
        println!("  Final requests: {}", final_metrics.total_requests);
        println!("  Successful parses: {}", final_metrics.successful_parses);
        println!("  Fallback uses: {}", final_metrics.fallback_uses);
        println!("  Average latency: {:.2}ms", final_metrics.average_latency_ms);

        // Verify metrics are being tracked
        assert!(final_metrics.total_requests >= initial_metrics.total_requests);
        assert!(final_metrics.average_latency_ms >= 0.0);
    }

    /// Regression test to ensure performance doesn't degrade
    #[tokio::test]
    async fn test_performance_regression() {
        let parser = IngredientParser::new();
        
        let test_ingredients = vec![
            "2 cups all-purpose flour".to_string(),
            "1 tablespoon olive oil".to_string(),
            "3 large eggs".to_string(),
        ];

        // Run multiple iterations to get stable measurements
        let mut total_time = Duration::new(0, 0);
        let iterations = 3;

        for _ in 0..iterations {
            let start_time = Instant::now();
            let _results = parser.parse_ingredients_batch(&test_ingredients).await;
            total_time += start_time.elapsed();
        }

        let avg_total_time = total_time / iterations as u32;
        let avg_time_per_ingredient = avg_total_time.as_millis() as f64 / test_ingredients.len() as f64;

        println!("Performance regression test:");
        println!("  Average total time: {}ms", avg_total_time.as_millis());
        println!("  Average per ingredient: {:.2}ms", avg_time_per_ingredient);

        // Regression threshold - should not exceed 100ms per ingredient on average
        assert!(avg_time_per_ingredient < 100.0, 
            "Performance regression detected: {:.2}ms per ingredient exceeds 100ms threshold", 
            avg_time_per_ingredient);
    }
}
