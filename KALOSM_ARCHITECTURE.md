# Kalosm Integration Architecture

## Overview

This document describes the architecture and implementation of Kalosm (local AI inference) integration for ingredient parsing in the JustCooked recipe application.

## Architecture Components

### 1. Core Parsing Module (`ingredient_parsing.rs`)

The main parsing module provides a unified interface for ingredient parsing with Kalosm as the primary method and regex parsing as fallback.

#### Key Components:
- **`IngredientParser`**: Main service class with singleton pattern
- **`ParsingMetrics`**: Performance tracking and monitoring
- **Model Management**: Lazy loading with warming capabilities
- **Concurrency Control**: Semaphore-based limiting (3 concurrent operations)

#### API Interface:
```rust
// Single ingredient parsing
pub async fn parse_ingredient(&self, ingredient_text: &str, section: Option<String>) -> Result<Option<Ingredient>>

// Batch processing
pub async fn parse_ingredients_batch(&self, ingredients: &[String]) -> Result<Vec<Ingredient>>

// Chunked processing for memory management
pub async fn parse_ingredients_chunked(&self, ingredients: &[String], chunk_size: usize) -> Result<Vec<Ingredient>>

// Performance monitoring
pub async fn get_metrics(&self) -> ParsingMetrics
pub async fn warm_model(&self) -> Result<()>
```

### 2. Configuration System (`kalosm_config.rs`)

Comprehensive configuration management for Kalosm parsing behavior.

#### Configuration Options:
- **`enabled`**: Enable/disable Kalosm parsing (default: true)
- **`model_selection`**: Model choice (auto, small, medium, large)
- **`confidence_threshold`**: Minimum confidence for accepting results (0.0-1.0)
- **`performance_mode`**: Preset configurations (fast, balanced, accurate)
- **`max_concurrent_operations`**: Concurrency limit (default: 3)
- **`enable_model_warming`**: Preload model on startup (default: true)
- **`parsing_timeout_ms`**: Timeout for individual operations (default: 5000ms)
- **`enable_fallback`**: Use regex fallback on errors (default: true)

#### Performance Mode Presets:
- **Fast**: Lower confidence threshold (0.5), higher concurrency (5), shorter timeout (2s)
- **Balanced**: Medium confidence (0.7), standard concurrency (3), standard timeout (5s)
- **Accurate**: Higher confidence (0.8), lower concurrency (2), longer timeout (10s)

### 3. Feedback System (`parsing_feedback.rs`)

User feedback collection and analysis for continuous improvement.

#### Feedback Components:
- **`ParsingFeedback`**: Individual parsing result feedback
- **`UserRating`**: Quality ratings (Excellent, Good, Fair, Poor, Terrible)
- **`ParsingCorrection`**: User-provided corrections
- **`FeedbackStatistics`**: Aggregated performance analytics

#### Analytics Features:
- Rating distribution tracking
- Method performance comparison (Kalosm vs regex)
- Confidence accuracy correlation
- Common correction patterns
- Automated improvement suggestions

### 4. Integration Points

#### Tauri Commands:
```rust
// Performance monitoring
get_kalosm_metrics() -> ParsingMetrics
warm_kalosm_model() -> Result<()>

// Configuration management
get_kalosm_configuration() -> KalosmConfig
update_kalosm_configuration(config: KalosmConfig) -> Result<()>
reset_kalosm_configuration() -> Result<()>
validate_kalosm_configuration(config: KalosmConfig) -> Result<bool>
export_kalosm_configuration() -> Result<String>
import_kalosm_configuration(json_config: String) -> Result<()>

// Feedback system
submit_parsing_feedback(feedback: ParsingFeedback) -> Result<()>
get_parsing_feedback_statistics() -> FeedbackStatistics
add_parsing_correction_suggestion(correction: ParsingCorrection) -> Result<()>
get_parsing_correction_suggestions(ingredient_text: String) -> Vec<ParsingCorrection>
clear_parsing_feedback_history() -> Result<()>
export_parsing_feedback_data() -> Result<String>
```

#### Batch Import Integration:
The batch import system (`batch_import.rs`) uses optimized chunked parsing:
```rust
// Enhanced batch processing with fallback
async fn parse_ingredients_with_kalosm_batch(&self, ingredient_strings: &[String]) -> Vec<Ingredient>
```

## Data Flow

### 1. Single Ingredient Parsing Flow
```
User Input → IngredientParser → Kalosm Model → Success/Failure
                ↓                                    ↓
         Semaphore Limit                      Regex Fallback
                ↓                                    ↓
         Metrics Update ← Performance Tracking ← Result
```

### 2. Batch Processing Flow
```
Ingredient List → Chunked Processing → Parallel Parsing → Result Aggregation
                        ↓                      ↓
                 Memory Management    Concurrency Control
                        ↓                      ↓
                 Progress Tracking ← Performance Metrics
```

### 3. Configuration Flow
```
Frontend UI → Tauri Commands → ConfigManager → Validation → Global State
                                     ↓              ↓
                              JSON Export/Import  Error Handling
```

### 4. Feedback Flow
```
User Interaction → Feedback Submission → FeedbackManager → Statistics
                                              ↓               ↓
                                      Correction Storage  Analytics
                                              ↓               ↓
                                    Improvement Suggestions  Reporting
```

## Error Handling Strategy

### 1. Graceful Degradation
- **Primary**: Kalosm parsing with confidence scoring
- **Fallback**: Regex parsing for reliability
- **Logging**: Comprehensive error tracking for debugging

### 2. Error Categories
- **Model Loading Errors**: Fallback to regex, log for investigation
- **Parsing Timeouts**: Automatic fallback with performance metrics
- **Memory Issues**: Chunked processing with reduced batch sizes
- **Configuration Errors**: Validation with helpful error messages

### 3. Recovery Mechanisms
- **Automatic Retry**: For transient failures
- **Circuit Breaker**: Disable Kalosm temporarily if failure rate is high
- **Performance Monitoring**: Track and alert on degraded performance

## Performance Characteristics

### 1. Benchmarks
- **Individual Parsing**: Target < 500ms per ingredient
- **Batch Parsing**: Target < 50ms per ingredient in batches
- **Memory Usage**: < 2GB total for model and inference
- **Startup Time**: < 10s for initial model loading

### 2. Optimization Strategies
- **Model Warming**: Preload and test inference on startup
- **Concurrency Limiting**: Prevent memory exhaustion
- **Chunked Processing**: Balance performance and memory usage
- **Metrics Collection**: Real-time performance monitoring

### 3. Scalability Considerations
- **Memory Management**: Automatic cleanup and limits
- **Thread Safety**: Arc/Mutex patterns for safe concurrency
- **Resource Pooling**: Singleton pattern for model sharing
- **Graceful Degradation**: Fallback ensures system availability

## Security and Privacy

### 1. Local Processing
- **No Network Calls**: All inference happens locally
- **Data Privacy**: Ingredient data never leaves the device
- **Model Storage**: Local model files only

### 2. Configuration Security
- **Input Validation**: All configuration parameters validated
- **Safe Defaults**: Secure default settings
- **Error Handling**: No sensitive data in error messages

## Testing Strategy

### 1. Unit Tests
- **Mock Testing**: Fast tests with mocked Kalosm responses
- **Configuration Testing**: Validation and edge cases
- **Error Handling**: Comprehensive failure scenario testing

### 2. Integration Tests
- **Real Model Testing**: End-to-end with actual Kalosm models
- **Performance Testing**: Benchmarks and regression tests
- **Memory Testing**: Long-running stability tests

### 3. Performance Tests
- **Latency Benchmarks**: Individual and batch parsing times
- **Memory Usage**: Peak and sustained memory consumption
- **Concurrency Testing**: Multiple simultaneous operations
- **Regression Testing**: Performance degradation detection

## Deployment Considerations

### 1. Model Distribution
- **Size Optimization**: Prefer models under 1GB
- **Download Strategy**: Optional download with fallback
- **Storage Management**: Efficient local storage

### 2. Feature Flags
- **Gradual Rollout**: Enable for subset of users initially
- **A/B Testing**: Compare Kalosm vs regex performance
- **Emergency Disable**: Quick disable capability

### 3. Monitoring
- **Performance Metrics**: Real-time parsing performance
- **Error Rates**: Kalosm vs fallback usage ratios
- **User Feedback**: Quality and satisfaction metrics
- **Resource Usage**: Memory and CPU consumption

## Future Enhancements

### 1. Model Improvements
- **Custom Training**: Train on recipe-specific data
- **Model Updates**: Automatic model version management
- **Multi-Model Support**: Different models for different use cases

### 2. Performance Optimizations
- **GPU Acceleration**: Leverage GPU when available
- **Model Quantization**: Reduce model size and memory usage
- **Caching**: Cache common parsing results

### 3. User Experience
- **Real-time Feedback**: Live parsing quality indicators
- **Learning System**: Adapt to user corrections
- **Confidence Visualization**: Show parsing confidence to users
