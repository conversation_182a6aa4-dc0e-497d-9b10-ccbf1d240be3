# Kalosm Integration Troubleshooting Guide

## Common Issues and Solutions

### 1. Model Loading Issues

#### Problem: "Failed to load Kalosm model"
**Symptoms:**
- Error messages about model loading failure
- All parsing falls back to regex
- Long startup times followed by errors

**Possible Causes:**
- Insufficient memory for model loading
- Corrupted model files
- Incompatible model version
- Missing dependencies

**Solutions:**
1. **Check Available Memory:**
   ```bash
   # Check system memory
   free -h  # Linux
   vm_stat  # macOS
   ```
   - Ensure at least 4GB free memory for model loading
   - Close other memory-intensive applications

2. **Verify Model Files:**
   - Check if model files exist in the expected location
   - Verify file integrity (not corrupted)
   - Re-download model if necessary

3. **Update Dependencies:**
   ```bash
   cd src-tauri
   cargo update
   ```

4. **Disable Model Warming:**
   - Set `enable_model_warming: false` in configuration
   - This delays model loading until first use

#### Problem: "Model loading timeout"
**Solutions:**
1. Increase timeout in configuration:
   ```json
   {
     "parsing_timeout_ms": 10000
   }
   ```

2. Enable model warming on startup:
   ```json
   {
     "enable_model_warming": true
   }
   ```

### 2. Performance Issues

#### Problem: Slow parsing performance
**Symptoms:**
- Parsing takes longer than expected
- UI becomes unresponsive during batch operations
- High CPU/memory usage

**Diagnostic Steps:**
1. **Check Performance Metrics:**
   ```javascript
   // Get current metrics
   const metrics = await invoke('get_kalosm_metrics');
   console.log('Average latency:', metrics.average_latency_ms);
   console.log('Success rate:', metrics.successful_parses / metrics.total_requests);
   ```

2. **Monitor Resource Usage:**
   - Check CPU usage during parsing
   - Monitor memory consumption
   - Look for memory leaks in long-running operations

**Solutions:**
1. **Adjust Performance Mode:**
   ```json
   {
     "performance_mode": "fast",
     "confidence_threshold": 0.5,
     "max_concurrent_operations": 5
   }
   ```

2. **Reduce Batch Sizes:**
   - Use smaller chunks for batch processing
   - Increase delays between batches

3. **Optimize Concurrency:**
   ```json
   {
     "max_concurrent_operations": 2
   }
   ```

#### Problem: Memory usage keeps increasing
**Symptoms:**
- Memory usage grows over time
- Application becomes slower
- Eventually crashes with out-of-memory errors

**Solutions:**
1. **Enable Chunked Processing:**
   - Use `parse_ingredients_chunked` instead of `parse_ingredients_batch`
   - Reduce chunk size (default: 10, try 5 or fewer)

2. **Clear Feedback History:**
   ```javascript
   await invoke('clear_parsing_feedback_history');
   ```

3. **Restart Application:**
   - Temporary solution for memory leaks
   - Report issue for investigation

### 3. Accuracy Issues

#### Problem: Poor parsing accuracy
**Symptoms:**
- Incorrect ingredient names, amounts, or units
- High fallback usage rate
- User complaints about parsing quality

**Diagnostic Steps:**
1. **Check Feedback Statistics:**
   ```javascript
   const stats = await invoke('get_parsing_feedback_statistics');
   console.log('Rating distribution:', stats.rating_distribution);
   console.log('Method performance:', stats.method_performance);
   ```

2. **Analyze Confidence Accuracy:**
   - Check if high confidence predictions are actually accurate
   - Look for patterns in failed predictions

**Solutions:**
1. **Adjust Confidence Threshold:**
   ```json
   {
     "confidence_threshold": 0.8
   }
   ```
   - Higher threshold = more fallback to regex but higher accuracy
   - Lower threshold = more Kalosm usage but potentially lower accuracy

2. **Switch Performance Mode:**
   ```json
   {
     "performance_mode": "accurate"
   }
   ```

3. **Collect User Feedback:**
   - Encourage users to rate parsing results
   - Use feedback to identify problem patterns

### 4. Configuration Issues

#### Problem: Configuration changes not taking effect
**Symptoms:**
- Settings appear to save but behavior doesn't change
- Error messages about invalid configuration
- Application reverts to default settings

**Solutions:**
1. **Validate Configuration:**
   ```javascript
   const isValid = await invoke('validate_kalosm_configuration', { config });
   if (!isValid) {
     console.error('Invalid configuration');
   }
   ```

2. **Check Configuration Format:**
   ```json
   {
     "enabled": true,
     "model_selection": "auto",
     "confidence_threshold": 0.7,
     "performance_mode": "balanced",
     "max_concurrent_operations": 3,
     "enable_model_warming": true,
     "parsing_timeout_ms": 5000,
     "enable_fallback": true,
     "enable_metrics": true,
     "enable_debug_logging": false
   }
   ```

3. **Reset to Defaults:**
   ```javascript
   await invoke('reset_kalosm_configuration');
   ```

### 5. Fallback Issues

#### Problem: Always falling back to regex parsing
**Symptoms:**
- No Kalosm parsing happening
- All results come from regex parser
- Metrics show 100% fallback usage

**Diagnostic Steps:**
1. **Check if Kalosm is enabled:**
   ```javascript
   const config = await invoke('get_kalosm_configuration');
   console.log('Kalosm enabled:', config.enabled);
   ```

2. **Check error logs:**
   - Look for repeated Kalosm errors
   - Check for model loading failures

**Solutions:**
1. **Enable Kalosm:**
   ```json
   {
     "enabled": true
   }
   ```

2. **Lower Confidence Threshold:**
   ```json
   {
     "confidence_threshold": 0.3
   }
   ```

3. **Warm Model Manually:**
   ```javascript
   await invoke('warm_kalosm_model');
   ```

### 6. Debugging Steps

#### Enable Debug Logging
```json
{
  "enable_debug_logging": true
}
```

#### Check Application Logs
1. **Frontend Console:**
   - Open browser developer tools
   - Check console for error messages
   - Look for failed Tauri command calls

2. **Backend Logs:**
   - Check Tauri application logs
   - Look for Rust error messages
   - Monitor parsing operation logs

#### Export Diagnostics Data
```javascript
// Export configuration
const config = await invoke('export_kalosm_configuration');

// Export feedback data
const feedback = await invoke('export_parsing_feedback_data');

// Export metrics
const metrics = await invoke('get_kalosm_metrics');
```

### 7. Performance Tuning Recommendations

#### For Low-End Devices:
```json
{
  "performance_mode": "fast",
  "max_concurrent_operations": 1,
  "parsing_timeout_ms": 3000,
  "enable_model_warming": false
}
```

#### For High-End Devices:
```json
{
  "performance_mode": "accurate",
  "max_concurrent_operations": 5,
  "parsing_timeout_ms": 10000,
  "enable_model_warming": true
}
```

#### For Batch Processing:
```json
{
  "performance_mode": "balanced",
  "max_concurrent_operations": 3,
  "parsing_timeout_ms": 5000
}
```

### 8. Emergency Procedures

#### Disable Kalosm Completely:
```json
{
  "enabled": false
}
```

#### Reset All Settings:
```javascript
await invoke('reset_kalosm_configuration');
await invoke('clear_parsing_feedback_history');
```

#### Force Fallback Mode:
```json
{
  "confidence_threshold": 1.0
}
```

### 9. Reporting Issues

When reporting issues, please include:

1. **System Information:**
   - Operating system and version
   - Available memory and CPU
   - Application version

2. **Configuration:**
   ```javascript
   const config = await invoke('export_kalosm_configuration');
   ```

3. **Performance Metrics:**
   ```javascript
   const metrics = await invoke('get_kalosm_metrics');
   ```

4. **Error Messages:**
   - Complete error messages from console
   - Steps to reproduce the issue
   - Expected vs actual behavior

5. **Sample Data:**
   - Ingredient strings that cause issues
   - Expected parsing results
   - Actual parsing results

### 10. Preventive Measures

1. **Regular Monitoring:**
   - Check performance metrics weekly
   - Monitor memory usage trends
   - Review user feedback regularly

2. **Configuration Backup:**
   - Export configuration before changes
   - Keep known-good configurations

3. **Update Management:**
   - Test configuration changes in development
   - Have rollback plan for production changes
   - Monitor performance after updates
